/* eslint-disable */
import { LightningElement, wire, track, api } from 'lwc';
import getRecords from '@salesforce/apex/cashFlowSessionController.getRecords';
import shareSessionWithApex from '@salesforce/apex/cashFlowSessionController.shareSessionWithApex';
import createRecordsFromDynamoData from '@salesforce/apex/cashFlowSessionController.createRecordsFromDynamoData';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';

const columns = [
    { label: "Company", fieldName: "Company__c", type: "Text", sortable: true, hideDefaultActions: true },
    { label: "User Name", fieldName: "User_Name__c", type: "Text", sortable: true, hideDefaultActions: true },
    { label: "User Email", fieldName: "User_Email__c", type: "Email", sortable: true, hideDefaultActions: true},
    { label: "Project", fieldName: "Project__c", type: "Text", sortable: true, hideDefaultActions: true },
    { label: "Contractor", fieldName: "Contractor__c", type: "Text", sortable: true, hideDefaultActions: true},
    { label: "Step", fieldName: "LatestStep__c", type: "Number", sortable: true, hideDefaultActions: true },
    { label: "Created", fieldName: "CreatedAt__c", type: "date", sortable: true, hideDefaultActions: true, typeAttributes:{ year:"numeric", month:"short", day:"2-digit", hour:"2-digit", minute:"2-digit"} },
    { label: "Updated", fieldName: "UpdatedAt__c", type: "date", sortable: true, hideDefaultActions: true, typeAttributes:{ year:"numeric", month:"short", day:"2-digit", hour:"2-digit", minute:"2-digit"} },
    { label: "Completed", fieldName: "CompletedAt__c", type: "date", sortable: true, hideDefaultActions: true, typeAttributes:{ year:"numeric", month:"short", day:"2-digit", hour:"2-digit", minute:"2-digit"} },
    {
        type: "button", initialWidth: 80, typeAttributes: {
            label: 'PDF', name: 'PDF', title: 'PDF', value: 'pdf', variant:'brand',
            class: { fieldName: 'pdfButtonClass' }
        }
    },
    {
        type: "button", initialWidth: 80, typeAttributes: {
            label: 'XLSX', name: 'XLSX', title: 'XLSX', value: 'xlsx', variant:'brand',
            class: { fieldName: 'xlsxButtonClass' }
        }
    },
    {
        type: "button", initialWidth: 90, typeAttributes: {
            label: 'Share', name: 'Share', title: 'Share', value: 'share', variant:'brand'
        }
    },
    {
        type: "button", initialWidth: 80, typeAttributes: {
            label: 'Load', name: 'Load', title: 'Load', value: 'load', variant:'brand'
        }
    },
    {
        type: "button", initialWidth: 150, typeAttributes: {
            label: 'Create Cashflow', name: 'CreateCashflow', title: 'Create Cashflow Records',
            value: 'create_cashflow', variant:'brand-outline'
        }
    }
];

export default class CashFlowSeesionCmp extends NavigationMixin(LightningElement)  {
    @api recordId;

    @track records = [];
    @track filteredRecords = [];
    @track currentPage = 1;
    @track totalPages = 1;
    @track paginatedRecords = [];
    @track recordsPerPage = '100'; // Default records per page, ensure it's a string for combobox

    columns = columns;
    isLoading = true;
    sortByUI;
    sortDirection;

    searchCompany = '';
    searchUserName = '';
    searchUserEmail = '';
    searchProject = '';
    searchContractor = '';

    filters = {
        Company__c: '',
        User_Name__c: '',
        User_Email__c: '',
        Project__c: '',
        Contractor__c: ''
    };

    get recordCountOptions() {
        return [
            { label: '100', value: '100' },
            { label: '200', value: '200' },
            { label: '500', value: '500' }
        ];
    }

    @wire(getRecords)
    wiredRecords({ error, data }) {
        this.isLoading = false;
        if (data) {
            this.records = data.map((rec) => {
                let config = {}; // For storing the parsed JSON configuration
                try {
                    // Configuration__c from Apex is the raw DynamoDB JSON string
                    if (rec.Configuration__c) {
                        // We don't need to parse it with DynamoHelper here in LWC for display purposes,
                        // but the raw string is passed to Apex for processing.
                        // For display fields, we parse the outer JSON string.
                        config = JSON.parse(rec.Configuration__c);
                    }
                } catch (e) {
                    console.error('Error parsing Configuration__c for record Id ' + rec.Id + ' for display fields:', e, rec.Configuration__c);
                    config = {};
                }
                const isCompletedNotBlank = !!rec.CompletedAt__c;

                // Accessing display fields from the *already standard* JSON part of Configuration__c (M, S structure)
                // This assumes the display fields are directly accessible after one JSON.parse,
                // and not requiring DynamoHelper.parse on the client-side for these specific display fields.
                // If these fields were also in DynamoDB format within Configuration__c, client-side parsing would be more complex.
                const details = config.details && config.details.M ? config.details.M : {};
                const projectName = details.projectName && details.projectName.S ? details.projectName.S : '';
                const userCompany = details.userCompany && details.userCompany.S ? details.userCompany.S : '';
                const userEmail = details.userEmail && details.userEmail.S ? details.userEmail.S : '';
                const userName = details.userName && details.userName.S ? details.userName.S : '';
                const contractorName = details.contractorName && details.contractorName.S ? details.contractorName.S : '';

                return {
                    ...rec,
                    // Store the raw Configuration__c string to be passed to Apex
                    RawConfiguration__c: rec.Configuration__c,
                    ProcessedJson__c: rec.Processed_Configuration__c,
                    Project__c: projectName,
                    Company__c: userCompany,
                    User_Email__c: userEmail,
                    User_Name__c: userName,
                    Contractor__c: contractorName,
                    pdfButtonClass: isCompletedNotBlank ? '' : 'slds-hide',
                    xlsxButtonClass: isCompletedNotBlank ? '' : 'slds-hide'
                };
            });
            this.filteredRecords = [...this.records];
            this.sortData('CreatedAt__c', 'desc');
            this.updatePagination();
        } else if (error) {
            console.error('Error fetching records:', error);
            this.showToast('Error Fetching Records', error.body ? error.body.message : error.message, 'error');
            this.records = [];
            this.filteredRecords = [];
        }
        this.updatePagination();
    }

    handleSearchChange(event) {
        const field = event.target.dataset.field;
        const value = event.target.value.toLowerCase();
        // Update actual search input values
        if(field === 'Company__c') this.searchCompany = event.target.value;
        if(field === 'User_Name__c') this.searchUserName = event.target.value;
        if(field === 'User_Email__c') this.searchUserEmail = event.target.value;
        if(field === 'Project__c') this.searchProject = event.target.value;
        if(field === 'Contractor__c') this.searchContractor = event.target.value;

        this.filters[field] = value;
        this.applyFilters();
    }

    applyFilters() {
        if (!this.records) {
            this.filteredRecords = [];
            this.updatePagination();
            return;
        }
        this.filteredRecords = this.records.filter(record => {
            return Object.keys(this.filters).every(field => {
                const recordValue = record[field] ? String(record[field]).toLowerCase() : '';
                return recordValue.includes(this.filters[field]);
            });
        });
        this.currentPage = 1;
        this.sortData(this.sortByUI, this.sortDirection);
    }

    updatePagination() {
        if (!this.filteredRecords || this.filteredRecords.length === 0) {
            this.totalPages = 1;
            this.paginatedRecords = [];
        } else {
            this.totalPages = Math.ceil(this.filteredRecords.length / parseInt(this.recordsPerPage, 10));
            const startIdx = (this.currentPage - 1) * parseInt(this.recordsPerPage, 10);
            const endIdx = startIdx + parseInt(this.recordsPerPage, 10);
            this.paginatedRecords = this.filteredRecords.slice(startIdx, endIdx);
        }
    }

    handleSort(event) {
        const { fieldName: sortedBy, sortDirection: direction } = event.detail;
        this.sortData(sortedBy, direction);
    }

    sortData(fieldName, direction) {
        if (!this.filteredRecords || !fieldName || !direction) { // Add checks for fieldName and direction
            this.updatePagination(); // Still update pagination if sort fields are not set
            return;
        }
        let sortResult = [...this.filteredRecords];
        sortResult.sort((a, b) => {
            let aValue = a[fieldName] === null || a[fieldName] === undefined ? '' : a[fieldName];
            let bValue = b[fieldName] === null || b[fieldName] === undefined ? '' : b[fieldName];

            if (fieldName === 'CreatedAt__c' || fieldName === 'UpdatedAt__c' || fieldName === 'CompletedAt__c') {
                aValue = aValue ? new Date(aValue).getTime() : (direction === 'asc' ? Infinity : -Infinity);
                bValue = bValue ? new Date(bValue).getTime() : (direction === 'asc' ? Infinity : -Infinity);
            }

            let comparison = 0;
            if (aValue > bValue) {
                comparison = 1;
            } else if (aValue < bValue) {
                comparison = -1;
            }
            return direction === 'asc' ? comparison : -comparison;
        });
        this.filteredRecords = sortResult;
        this.sortByUI = fieldName;
        this.sortDirection = direction;
        this.updatePagination();
    }


    handlePreviousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.updatePagination();
        }
    }

    handlePageNumberChange(event) {
        const enteredPageNumber = parseInt(event.target.value, 10);
        if (!isNaN(enteredPageNumber) && enteredPageNumber >= 1 && enteredPageNumber <= this.totalPages) {
            this.currentPage = enteredPageNumber;
        } else {
            event.target.value = this.currentPage;
        }
        this.updatePagination();
    }

    handleNextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.updatePagination();
        }
    }

    get disablePrevious() {
        return this.currentPage === 1;
    }

    get disableNext() {
        return this.currentPage === this.totalPages || this.totalPages === 0 || this.totalPages === 1;
    }

    handleRecordsPerPageChange(event) {
        this.recordsPerPage = event.detail.value;
        this.currentPage = 1;
        this.updatePagination();
    }

    handleRowAction(event) {
        const actionName = event.detail.action.name;
        const row = event.detail.row;
        const baseUrl = '[https://mobilizationfunding.com/](https://mobilizationfunding.com/)';

        switch (actionName) {
            case 'PDF':
                this.handleFileAction(baseUrl, row.SessionId__c, 'pdf');
                break;
            case 'XLSX':
                this.handleFileAction(baseUrl, row.SessionId__c, 'xlsx');
                break;
            case 'Share':
                this.handleShareAction(row.AccountId__c, row.SessionId__c);
                break;
            case 'Load':
                this.handleLoadClick(row.AccountId__c, row.SessionId__c);
                break;
            case 'CreateCashflow':
                this.handleCreateCashflowAction(row);
                break;
            default:
                console.warn('Unhandled row action:', actionName);
        }
    }

    handleFileAction(baseUrl, sessionId, actionType){
        if (!sessionId) {
            this.showToast('Error', 'Session ID is missing.', 'error');
            return;
        }
        let endpoint = actionType === 'pdf' ? 'summary.pdf' : 'projection.xlsx';
        let fileUrl = `${baseUrl}${sessionId}/${endpoint}`;
        console.log(`Opening ${actionType.toUpperCase()} at: ${fileUrl}`);
        window.open(fileUrl, '_blank');
    }

    handleLoadClick(accountId, sessionId) {
        if (!sessionId || !accountId) {
            this.showToast('Error', 'Session ID or Account ID is missing for Load action.', 'error');
            return;
        }
        let endpointUrl = `https://mobilizationfunding.com/portal/cash-flow?session_id=${sessionId}&account_id=${accountId}`;
        window.open(endpointUrl, '_blank');
    }

    handleShareAction(accountId, sessionId) {
        if (!sessionId || !accountId) {
            this.showToast('Error', 'Session ID or Account ID is missing for Share action.', 'error');
            return;
        }
        this.isLoading = true;
        let endpointUrl = `https://lmn3bgz88h.execute-api.us-east-1.amazonaws.com/accounts/${accountId}/sessions/${sessionId}/share`;

        shareSessionWithApex({ endpointUrl })
            .then(apiResult => {
                console.log('Share API Response:', apiResult);
                navigator.clipboard.writeText(endpointUrl)
                .then(() => {
                    this.showToast('Success', 'Share link copied to clipboard.', 'success');
                })
                .catch(clipboardError => {
                    console.error('Error copying to clipboard: ', clipboardError);
                    this.showToast('Warning', 'Could not copy link to clipboard. Please copy manually.', 'warning');
                });
            })
            .catch(error => {
                console.error('Share Action Error:', error);
                this.showToast('Error Sharing', 'Failed to share session. ' + (error.body ? error.body.message : error.message), 'error');
            })
            .finally(() => {
                this.isLoading = false;
            });
    }

    handleCreateCashflowAction(row) {
        debugger;
        const accountId = row.AccountId__c;
        const dynamoJsonString = row.ProcessedJson__c; // Use the raw string

        // if (!accountId) {
        //     this.showToast('Error', 'Account ID is missing for this session.', 'error');
        //     return;
        // }
        if (!dynamoJsonString) {
            this.showToast('Error', 'Configuration data (DynamoDB JSON) is missing for this session.', 'error');
            return;
        }

        this.isLoading = true;
        
        console.log('this.recordId -> ' + this.recordId);
        
        createRecordsFromDynamoData({
            accountId: accountId,
            dynamoJsonString: dynamoJsonString,
            projectId: this.recordId
        })
        .then(result => {
            if (result.success) {
                let successMessage = 'Cashflow records created successfully.';
                if(result.cashflowId) successMessage += ` Cashflow Name: ${result.cashflowName}.`;
                if(result.projectId) successMessage += ` Project Name: ${result.projectName}.`;
                if(result.lineItemIds && result.lineItemIds.length > 0) successMessage += ` ${result.lineItemIds.length} Line Item(s) created.`;

                this.showToast('Success', successMessage, 'success');
                this[NavigationMixin.Navigate]({
                    type: 'standard__navItemPage',
                    attributes: {
                        apiName: 'CashFlow'
                    },
                    state: {
                        c__recordId: result.cashflowId,
                        c__projectId: result.projectId 
                    }
                });
            } else {
                console.error('Error creating Cashflow records:', result.errorMessage);
                this.showToast('Error Creating Records', result.errorMessage || 'An unknown error occurred.', 'error');
            }
        })
        .catch(error => {
            console.error('Error calling createRecordsFromDynamoData:', error);
            this.showToast('Apex Call Error', error.body ? error.body.message : error.message, 'error');
        })
        .finally(() => {
            this.isLoading = false;
        });
    }

    showToast(title, message, variant) {
        const evt = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant,
            mode: variant === 'error' ? 'sticky' : 'dismissable'
        });
        this.dispatchEvent(evt);
    }
}